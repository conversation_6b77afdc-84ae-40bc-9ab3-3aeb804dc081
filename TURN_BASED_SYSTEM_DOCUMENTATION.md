# Cluedo Turn-Based System Documentation

## Overview

The Cluedo game implements a comprehensive turn-based system that manages player turns, validates actions, and maintains game state through WebSocket communication. The system ensures fair gameplay by enforcing turn order and validating that only the current player can perform actions.

## Architecture

### Core Components

1. **TurnService** - Business logic for turn management and validation
2. **Turn<PERSON><PERSON>roller** - WebSocket endpoints for turn-based actions
3. **TurnState Enum** - Defines all possible game states
4. **TurnStateResponse** - Standardized response format for turn updates

## Turn States

The game uses the following turn states to manage gameplay flow:

```java
public enum TurnState {
    WAITING_FOR_PLAYERS,    // <PERSON>bby created, waiting for players to join
    WAITING_FOR_START,      // At least 3 players, waiting for host to start
    PLAYERS_TURN_ROLL_DICE, // Current player needs to roll dice
    PLAYERS_TURN_MOVE,      // Current player needs to move
    PLAYERS_TURN_SUSPECT,   // Current player can make a suggestion (only if in room)
    PLAYERS_TURN_SOLVE,     // Current player can make an accusation (anytime during turn)
    PLAYERS_TURN_END,       // Current player's turn is ending
    PLAYER_HAS_WON          // Game finished, someone won
}
```

## Turn Flow

### 1. Game Initialization
- Lobby starts in `WAITING_FOR_PLAYERS` state
- When 3+ players join → `WAITING_FOR_START`
- Host starts game → `PLAYERS_TURN_ROLL_DICE` for first player

### 2. Player Turn Sequence
1. **Roll Dice Phase** (`PLAYERS_TURN_ROLL_DICE`)
   - Player must roll dice to determine movement
   - Advances to `PLAYERS_TURN_MOVE`

2. **Movement Phase** (`PLAYERS_TURN_MOVE`)
   - Player moves their piece on the board
   - If player ends in a room → `PLAYERS_TURN_SUSPECT`
   - If player ends in hallway → `PLAYERS_TURN_END`

3. **Suggestion Phase** (`PLAYERS_TURN_SUSPECT`) - Optional
   - Only available if player is in a room
   - Player can make a suggestion
   - After suggestion → `PLAYERS_TURN_END`

4. **Accusation Phase** (`PLAYERS_TURN_SOLVE`) - Optional
   - Available during any turn phase (except PLAYERS_TURN_END)
   - Player can make an accusation to win the game
   - Correct accusation → `PLAYER_HAS_WON`
   - Wrong accusation → Player eliminated, continue game

5. **Turn End** (`PLAYERS_TURN_END`)
   - 2-second delay for UI updates
   - Advances to next player's `PLAYERS_TURN_ROLL_DICE`

## WebSocket Endpoints

### Turn Management Endpoints

| Endpoint | Purpose | Request Type | Response Topic | Response Type |
|----------|---------|--------------|----------------|---------------|
| `/app/initializeTurns/{lobbyId}` | Initialize turn system when game starts | None | `/topic/turnsInitialized/{lobbyId}` | `TurnStateResponse` |
| `/app/getTurnState/{lobbyId}` | Get current turn state | None | `/topic/currentTurnState/{lobbyId}` | `TurnStateResponse` |
| `/app/checkPlayerTurn/{lobbyId}` | Check if specific player's turn | `{playerName}` | `/topic/playerTurnCheck/{lobbyId}` | `Map<String, Object>` |

### Game Action Endpoints

| Endpoint | Purpose | Request Type | Response Topic | Response Type |
|----------|---------|--------------|----------------|---------------|
| `/app/rollDice/{lobbyId}` | Roll dice to start turn | `TurnActionRequest` | `/topic/diceRolled/{lobbyId}` | `TurnStateResponse` |
| `/app/completeMovement/{lobbyId}` | Complete player movement | `TurnActionRequest` | `/topic/movementCompleted/{lobbyId}` | `TurnStateResponse` |
| `/app/makeSuggestion/{lobbyId}` | Make a suggestion (room only) | `SuggestionRequest` | `/topic/suggestionMade/{lobbyId}` | `Map<String, Object>` |
| `/app/makeAccusation/{lobbyId}` | Make an accusation | `AccusationRequest` | `/topic/accusationMade/{lobbyId}` | `Map<String, Object>` |

### Administrative Endpoints

| Endpoint | Purpose | Request Type | Response Topic | Response Type |
|----------|---------|--------------|----------------|---------------|
| `/app/skipTurn/{lobbyId}` | Skip current player's turn | `{reason}` | `/topic/turnSkipped/{lobbyId}` | `TurnStateResponse` |
| `/app/endGame/{lobbyId}` | Force end game (admin) | None | `/topic/gameEnded/{lobbyId}` | `Map<String, Object>` |

## Request/Response Formats

### TurnActionRequest
```json
{
    "playerName": "string",
    "actionType": "string",
    "diceValue": "number"
}
```

### SuggestionRequest
```json
{
    "playerName": "string",
    "suspect": "string",
    "weapon": "string",
    "room": "string"
}
```

### AccusationRequest
```json
{
    "playerName": "string",
    "suspect": "string",
    "weapon": "string",
    "room": "string"
}
```

### TurnStateResponse
```json
{
    "lobbyId": "string",
    "currentPlayerName": "string",
    "currentPlayerIndex": "number",
    "turnState": "TurnState",
    "canMakeAccusation": "boolean",
    "canMakeSuggestion": "boolean",
    "diceValue": "number",
    "message": "string"
}
```

## Validation Rules

### Turn Validation
- Only the current player can perform actions
- Actions must be performed in the correct turn state
- Dice rolling only allowed in `PLAYERS_TURN_ROLL_DICE`
- Movement only allowed in `PLAYERS_TURN_MOVE`
- Suggestions only allowed in `PLAYERS_TURN_SUSPECT` (when in room)
- Accusations allowed during any active turn phase

### Player Validation
- Player must be active (not eliminated)
- Player must be in the current game
- Player name must match the current player's turn

### State Validation
- Game must be in progress (not ended)
- Lobby must exist and have an active game
- Turn state must be appropriate for the requested action

## Error Handling

The system provides comprehensive error handling with descriptive messages:

- **Invalid Turn**: "It's not [player]'s turn"
- **Invalid State**: "Invalid turn state for [action]"
- **Player Not Found**: "Player not found in game"
- **Game Not Found**: "Game not found for lobby"
- **Action Not Allowed**: "Cannot [action] at this time"

## Frontend Integration

### Subscribing to Turn Updates
```javascript
// Subscribe to turn state changes
stompClient.subscribe('/topic/turnsInitialized/' + lobbyId, handleTurnInitialized);
stompClient.subscribe('/topic/diceRolled/' + lobbyId, handleDiceRolled);
stompClient.subscribe('/topic/movementCompleted/' + lobbyId, handleMovementCompleted);
stompClient.subscribe('/topic/suggestionMade/' + lobbyId, handleSuggestionMade);
stompClient.subscribe('/topic/accusationMade/' + lobbyId, handleAccusationMade);
stompClient.subscribe('/topic/currentTurnState/' + lobbyId, handleTurnState);
```

### Sending Turn Actions
```javascript
// Roll dice
stompClient.send('/app/rollDice/' + lobbyId, {}, JSON.stringify({
    playerName: currentPlayer.name,
    diceValue: 0  // Server generates random value
}));

// Make suggestion
stompClient.send('/app/makeSuggestion/' + lobbyId, {}, JSON.stringify({
    playerName: currentPlayer.name,
    suspect: "Colonel Mustard",
    weapon: "Knife",
    room: "Kitchen"
}));

// Make accusation
stompClient.send('/app/makeAccusation/' + lobbyId, {}, JSON.stringify({
    playerName: currentPlayer.name,
    suspect: "Colonel Mustard",
    weapon: "Knife", 
    room: "Kitchen"
}));
```

## Key Features

### Real-time Synchronization
- All players receive immediate updates when turn state changes
- WebSocket communication ensures all clients stay synchronized
- Automatic turn progression with built-in delays for UI updates

### Comprehensive State Management
- Complete turn state tracking for each lobby
- Player turn validation and enforcement
- Game end detection and handling

### Flexible Action System
- Accusations can be made during any turn phase
- Suggestions only available when in rooms
- Turn skipping for eliminated players or timeouts

### Robust Error Handling
- Detailed error messages for debugging
- Graceful handling of invalid actions
- Automatic recovery from error states

This turn-based system ensures fair, organized gameplay while providing a smooth user experience through real-time WebSocket communication.

## Implementation Examples

### Complete Turn Sequence Example

```javascript
// 1. Initialize game turns
stompClient.send('/app/initializeTurns/' + lobbyId, {}, {});

// 2. Player rolls dice
stompClient.send('/app/rollDice/' + lobbyId, {}, JSON.stringify({
    playerName: "Alice",
    diceValue: 0
}));
// Response: TurnState changes to PLAYERS_TURN_MOVE

// 3. Player completes movement
stompClient.send('/app/completeMovement/' + lobbyId, {}, JSON.stringify({
    playerName: "Alice"
}));
// Response: TurnState changes to PLAYERS_TURN_SUSPECT (if in room) or PLAYERS_TURN_END

// 4. If in room, player can make suggestion
stompClient.send('/app/makeSuggestion/' + lobbyId, {}, JSON.stringify({
    playerName: "Alice",
    suspect: "Colonel Mustard",
    weapon: "Knife",
    room: "Kitchen"
}));
// Response: Suggestion processed, turn ends, next player's turn begins
```

### Turn State Monitoring

```javascript
function handleTurnStateUpdate(message) {
    const turnData = JSON.parse(message.body);

    // Update UI based on turn state
    switch(turnData.turnState) {
        case 'PLAYERS_TURN_ROLL_DICE':
            if (turnData.currentPlayerName === currentPlayer.name) {
                enableDiceRollButton();
            } else {
                showWaitingMessage(turnData.currentPlayerName + " is rolling dice");
            }
            break;

        case 'PLAYERS_TURN_MOVE':
            if (turnData.currentPlayerName === currentPlayer.name) {
                enableMovementControls();
                showDiceResult(turnData.diceValue);
            }
            break;

        case 'PLAYERS_TURN_SUSPECT':
            if (turnData.currentPlayerName === currentPlayer.name) {
                enableSuggestionControls();
            }
            break;
    }

    // Update action buttons based on permissions
    updateAccusationButton(turnData.canMakeAccusation);
    updateSuggestionButton(turnData.canMakeSuggestion);
}
```

## Advanced Features

### Turn Timeout Handling
The system supports automatic turn skipping for inactive players:

```javascript
// Set up turn timeout (frontend implementation)
let turnTimer;

function startTurnTimer(playerName, timeoutSeconds = 60) {
    clearTimeout(turnTimer);

    if (playerName === currentPlayer.name) {
        turnTimer = setTimeout(() => {
            // Auto-skip turn after timeout
            stompClient.send('/app/skipTurn/' + lobbyId, {}, JSON.stringify({
                reason: "Turn timeout"
            }));
        }, timeoutSeconds * 1000);
    }
}

function clearTurnTimer() {
    clearTimeout(turnTimer);
}
```

### Game State Persistence
The turn system maintains state across WebSocket reconnections:

```javascript
// On reconnection, get current turn state
function onReconnect() {
    stompClient.send('/app/getTurnState/' + lobbyId, {}, {});
    stompClient.send('/app/checkPlayerTurn/' + lobbyId, {}, JSON.stringify({
        playerName: currentPlayer.name
    }));
}
```

## Troubleshooting

### Common Issues

1. **"Invalid turn attempt"**
   - Ensure it's the correct player's turn
   - Check that the action is valid for current turn state
   - Verify player is not eliminated

2. **"Cannot make suggestion"**
   - Player must be in a room (not hallway)
   - Must be in PLAYERS_TURN_SUSPECT state
   - Must be the current player's turn

3. **Turn state synchronization issues**
   - Check WebSocket connection status
   - Verify subscription to correct topics
   - Use getTurnState endpoint to resync

### Debug Information

Enable detailed logging to troubleshoot turn issues:

```javascript
// Log all turn-related messages
stompClient.subscribe('/topic/diceRolled/' + lobbyId, (message) => {
    console.log('Dice rolled:', JSON.parse(message.body));
});

stompClient.subscribe('/topic/currentTurnState/' + lobbyId, (message) => {
    console.log('Turn state:', JSON.parse(message.body));
});
```

## Performance Considerations

- Turn state is maintained in memory for fast access
- WebSocket messages are lightweight JSON objects
- Automatic cleanup of ended games prevents memory leaks
- Turn transitions include small delays to allow UI updates

## Security Features

- Server-side validation prevents cheating
- Player authentication required for all actions
- Turn order enforcement prevents out-of-turn actions
- Game state is authoritative on server side
